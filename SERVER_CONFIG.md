# Analysis Agent 服务器配置说明

## 📋 概述

Analysis Agent 服务器现在支持通过环境变量和命令行参数来配置各种设置，包括端口、超时时间、最大迭代次数等。

## 🔧 配置方式

### 1. 环境变量配置（推荐）

在 `.env` 文件中设置以下配置：

```bash
# ================================
# Agent配置
# ================================
AGENT_MAX_ITERATIONS=10    # 最大工具调用次数
AGENT_TIMEOUT=300          # Agent超时时间（秒）

# ================================
# 服务器配置
# ================================
SERVER_HOST=0.0.0.0        # 服务器监听地址
SERVER_PORT=8001           # 服务器端口

# ================================
# 本地LLM配置
# ================================
LOCAL_LLM_TIMEOUT=60       # LLM请求超时时间（秒）
```

### 2. 命令行参数配置

可以通过命令行参数临时覆盖环境变量设置：

```bash
# 使用默认配置启动
python start_analysis_agent_server.py

# 指定端口启动
python start_analysis_agent_server.py --port 8888

# 指定主机和端口启动
python start_analysis_agent_server.py --host 127.0.0.1 --port 8888

# 查看当前配置
python start_analysis_agent_server.py --show-config
```

### 3. 直接启动（高级用户）

```bash
# 直接使用 examples/analysis_agent_server.py
python examples/analysis_agent_server.py --mode server --host 0.0.0.0 --port 8001
```

## 📊 配置项说明

| 配置项 | 环境变量 | 默认值 | 说明 |
|--------|----------|--------|------|
| 服务器地址 | `SERVER_HOST` | `0.0.0.0` | 服务器监听的IP地址 |
| 服务器端口 | `SERVER_PORT` | `8001` | 服务器监听的端口 |
| 最大迭代次数 | `AGENT_MAX_ITERATIONS` | `10` | Agent最多可以调用工具的次数 |
| Agent超时 | `AGENT_TIMEOUT` | `300` | Agent处理请求的超时时间（秒） |
| LLM超时 | `LOCAL_LLM_TIMEOUT` | `60` | 单次LLM请求的超时时间（秒） |

## 🎯 使用示例

### 开发环境
```bash
# 修改 .env 文件
SERVER_HOST=127.0.0.1
SERVER_PORT=8001

# 启动服务器
python start_analysis_agent_server.py
```

### 生产环境
```bash
# 修改 .env 文件
SERVER_HOST=0.0.0.0
SERVER_PORT=80
AGENT_TIMEOUT=600
AGENT_MAX_ITERATIONS=15

# 启动服务器
python start_analysis_agent_server.py
```

### 临时测试
```bash
# 不修改配置文件，直接指定参数
python start_analysis_agent_server.py --port 9999
```

## 🔍 配置验证

使用以下命令查看当前生效的配置：

```bash
python start_analysis_agent_server.py --show-config
```

输出示例：
```
🔧 Analysis Agent 服务器配置
==================================================
📡 服务器地址: 0.0.0.0
   (从环境变量读取)
🔌 服务器端口: 8001
   (从环境变量读取)
🔄 最大迭代次数: 10
⏱️ 超时时间: 300秒
🌡️ LLM超时: 60秒
==================================================
```

## 💡 最佳实践

1. **开发环境**：使用 `.env` 文件配置，便于版本控制和团队协作
2. **生产环境**：使用环境变量或配置管理系统
3. **临时测试**：使用命令行参数快速调整配置
4. **配置验证**：启动前使用 `--show-config` 确认配置正确

## 🚨 注意事项

- 命令行参数优先级高于环境变量
- 修改 `.env` 文件后需要重启服务器才能生效
- 端口被占用时会启动失败，请选择其他端口
- 超时时间设置过短可能导致复杂查询失败
